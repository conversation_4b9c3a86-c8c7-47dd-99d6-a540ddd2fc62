import { Test, TestingModule } from '@nestjs/testing';
import { FileReaderService } from './file-reader.service';
import {
  UnsupportedFileTypeException,
  MissingRequiredColumnsException,
} from '../exceptions/file-reader.exceptions';
import { Readable } from 'stream';

describe('FileReaderService', () => {
  let service: FileReaderService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [FileReaderService],
    }).compile();

    service = module.get<FileReaderService>(FileReaderService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('readFile', () => {
    it('should parse CSV file successfully', async () => {
      const csvContent =
        'name,email,age\nJohn <PERSON>e,<EMAIL>,30\nJ<PERSON>,<EMAIL>,25';
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'test.csv',
        encoding: '7bit',
        mimetype: 'text/csv',
        size: csvContent.length,
        buffer: Buffer.from(csvContent),
        destination: '',
        filename: '',
        path: '',
        stream: null as unknown as Readable,
      };

      const result = await service.readFile(mockFile);

      expect(result.data).toHaveLength(2);
      expect(result.totalRows).toBe(result.data.length);
      expect(result.columns).toEqual(['name', 'email', 'age']);

      // Verificar se os dados foram parseados corretamente
      const firstRow = result.data[0];
      const secondRow = result.data[1];

      expect(firstRow.name).toBe('John Doe');
      expect(firstRow.email).toBe('<EMAIL>');
      expect(firstRow.age).toBe('30');

      expect(secondRow.name).toBe('Jane Smith');
      expect(secondRow.email).toBe('<EMAIL>');
      expect(secondRow.age).toBe('25');
    });

    it('should validate required columns', async () => {
      const csvContent = 'name,email\nJohn Doe,<EMAIL>';
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'test.csv',
        encoding: '7bit',
        mimetype: 'text/csv',
        size: csvContent.length,
        buffer: Buffer.from(csvContent),
        destination: '',
        filename: '',
        path: '',
        stream: null as unknown as Readable,
      };

      await expect(
        service.readFile(mockFile, {
          requiredColumns: ['name', 'email', 'age'],
        }),
      ).rejects.toThrow(MissingRequiredColumnsException);
    });

    it('should throw UnsupportedFileTypeException for unsupported file types', async () => {
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'test.txt',
        encoding: '7bit',
        mimetype: 'text/plain',
        size: 100,
        buffer: Buffer.from('some content'),
        destination: '',
        filename: '',
        path: '',
        stream: null as unknown as Readable,
      };

      await expect(service.readFile(mockFile)).rejects.toThrow(
        UnsupportedFileTypeException,
      );
    });

    it('should handle CSV with custom delimiter', async () => {
      const csvContent = 'name;email;age\nJohn Doe;<EMAIL>;30';
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'test.csv',
        encoding: '7bit',
        mimetype: 'text/csv',
        size: csvContent.length,
        buffer: Buffer.from(csvContent),
        destination: '',
        filename: '',
        path: '',
        stream: null as unknown as Readable,
      };

      const result = await service.readFile(mockFile, { delimiter: ';' });

      expect(result.data).toHaveLength(1);
      expect(result.totalRows).toBe(result.data.length);
      expect(result.columns).toEqual(['name', 'email', 'age']);

      const row = result.data[0];
      expect(row.name).toBe('John Doe');
      expect(row.email).toBe('<EMAIL>');
      expect(row.age).toBe('30');
    });

    it('should trim values by default', async () => {
      const csvContent = 'name,email\n  John Doe  ,  <EMAIL>  ';
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'test.csv',
        encoding: '7bit',
        mimetype: 'text/csv',
        size: csvContent.length,
        buffer: Buffer.from(csvContent),
        destination: '',
        filename: '',
        path: '',
        stream: null as unknown as Readable,
      };

      const result = await service.readFile(mockFile);

      expect(result.data).toHaveLength(1);
      expect(result.columns).toEqual(['name', 'email']);

      const row = result.data[0];
      expect(row.name).toBe('John Doe');
      expect(row.email).toBe('<EMAIL>');
    });

    it('should skip empty lines by default', async () => {
      const csvContent =
        'name,email\nJohn Doe,<EMAIL>\nJane Smith,<EMAIL>';
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'test.csv',
        encoding: '7bit',
        mimetype: 'text/csv',
        size: csvContent.length,
        buffer: Buffer.from(csvContent),
        destination: '',
        filename: '',
        path: '',
        stream: null as unknown as Readable,
      };

      const result = await service.readFile(mockFile);

      expect(result.data).toHaveLength(2);
      expect(result.totalRows).toBe(2);
      expect(result.columns).toEqual(['name', 'email']);

      expect(result.data[0].name).toBe('John Doe');
      expect(result.data[0].email).toBe('<EMAIL>');
      expect(result.data[1].name).toBe('Jane Smith');
      expect(result.data[1].email).toBe('<EMAIL>');
    });
  });

  describe('getFileInfo', () => {
    it('should return file information for CSV', async () => {
      const csvContent = 'name,email,age\nJohn Doe,<EMAIL>,30';
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'test.csv',
        encoding: '7bit',
        mimetype: 'text/csv',
        size: csvContent.length,
        buffer: Buffer.from(csvContent),
        destination: '',
        filename: '',
        path: '',
        stream: null as unknown as Readable,
      };

      const result = await service.getFileInfo(mockFile);

      expect(result.filename).toBe('test.csv');
      expect(result.size).toBe(csvContent.length);
      expect(result.type).toBe('csv');
      expect(result.columns).toEqual(['name', 'email', 'age']);
    });

    it('should handle files without extension', async () => {
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'test',
        encoding: '7bit',
        mimetype: 'text/plain',
        size: 100,
        buffer: Buffer.from('content'),
        destination: '',
        filename: '',
        path: '',
        stream: null as unknown as Readable,
      };

      await expect(service.getFileInfo(mockFile)).rejects.toThrow(
        UnsupportedFileTypeException,
      );
    });
  });
});
