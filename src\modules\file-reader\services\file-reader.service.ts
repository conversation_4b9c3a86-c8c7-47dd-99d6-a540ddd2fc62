/* eslint-disable @typescript-eslint/no-unnecessary-type-assertion */
import { Injectable, Logger } from '@nestjs/common';
import { Readable } from 'stream';
import * as csv from 'csv-parser';
import * as XLSX from 'xlsx';
import {
  FileReaderOptions,
  ParsedFileResult,
  FileValidationResult,
  SupportedFileType,
} from '../interfaces/file-reader.interface';
import {
  UnsupportedFileTypeException,
  FileCorruptedException,
  MissingRequiredColumnsException,
  FileProcessingException,
} from '../exceptions/file-reader.exceptions';

@Injectable()
export class FileReaderService {
  private readonly logger = new Logger(FileReaderService.name);

  /**
   * Reads and parses a file from Express.Multer.File
   * @param file - The uploaded file
   * @param options - Parsing options
   * @returns Parsed file result
   */
  async readFile(
    file: Express.Multer.File,
    options: FileReaderOptions = {},
  ): Promise<ParsedFileResult> {
    try {
      this.logger.log(`Processing file: ${file.originalname}`);

      const fileType = this.getFileType(file.originalname);

      let result: ParsedFileResult;

      switch (fileType) {
        case SupportedFileType.CSV:
          result = await this.readCsvFile(file, options);
          break;
        case SupportedFileType.XLSX:
          result = this.readXlsxFile(file, options);
          break;
        default:
          throw new UnsupportedFileTypeException(fileType);
      }

      // Validate required columns if specified
      if (options.requiredColumns && options.requiredColumns.length > 0) {
        const validation = this.validateRequiredColumns(
          result.columns,
          options.requiredColumns,
        );
        if (!validation.isValid) {
          throw new MissingRequiredColumnsException(validation.missingColumns);
        }
      }

      this.logger.log(
        `Successfully processed ${result.totalRows} rows from ${file.originalname}`,
      );
      return result;
    } catch (error) {
      this.logger.error(`Error processing file ${file.originalname}:`, error);

      if (
        error instanceof UnsupportedFileTypeException ||
        error instanceof MissingRequiredColumnsException
      ) {
        throw error;
      }

      throw new FileProcessingException(
        (error as Error).message || (error as string),
      );
    }
  }

  /**
   * Reads CSV file using streams
   */
  private async readCsvFile(
    file: Express.Multer.File,
    options: FileReaderOptions,
  ): Promise<ParsedFileResult> {
    return new Promise((resolve, reject) => {
      const data: Record<string, unknown>[] = [];
      const errors: string[] = [];
      let columns: string[] = [];

      const stream = Readable.from(file.buffer);

      const csvOptions = {
        separator: options.delimiter || ',',
        skipEmptyLines: options.skipEmptyLines !== false,
      };

      stream
        .pipe(csv(csvOptions))
        .on('headers', (headers: string[]) => {
          columns = headers.map((header) =>
            options.trimValues !== false ? header.trim() : header,
          );
        })
        .on('data', (row: Record<string, unknown>) => {
          try {
            // Trim values if option is enabled (default: true)
            if (options.trimValues !== false) {
              Object.keys(row).forEach((key) => {
                if (typeof row[key] === 'string') {
                  row[key] = row[key].trim();
                }
              });
            }

            data.push(row);
          } catch (error) {
            errors.push(
              `Row ${data.length + 1}: ${(error as Error).message || (error as string)}`,
            );
          }
        })
        .on('end', () => {
          resolve({
            data,
            totalRows: data.length,
            columns,
            errors: errors.length > 0 ? errors : undefined,
          });
        })
        .on('error', (error) => {
          reject(
            new FileCorruptedException(`CSV parsing error: ${error.message}`),
          );
        });
    });
  }

  /**
   * Reads XLSX file
   */
  private readXlsxFile(
    file: Express.Multer.File,
    options: FileReaderOptions,
  ): ParsedFileResult {
    try {
      const workbook = XLSX.read(file.buffer, { type: 'buffer' });

      if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
        throw new FileCorruptedException('No sheets found in Excel file');
      }

      // Use first sheet
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];

      if (!worksheet) {
        throw new FileCorruptedException(`Sheet "${sheetName}" not found`);
      }

      // Convert to JSON
      const jsonData = XLSX.utils.sheet_to_json(worksheet, {
        header: 1,
        defval: '',
        blankrows: !options.skipEmptyLines,
      }) as unknown[][];

      if (jsonData.length === 0) {
        return {
          data: [] as Record<string, unknown>[],
          totalRows: 0,
          columns: [],
        };
      }

      // First row as headers
      const headers = jsonData[0].map((header: unknown) =>
        options.trimValues !== false ? String(header).trim() : String(header),
      );

      const dataRows = jsonData.slice(1);
      const data: Record<string, unknown>[] = [];

      dataRows.forEach((row) => {
        // Skip empty rows if option is enabled
        if (options.skipEmptyLines !== false && row.every((cell) => !cell)) {
          return;
        }

        const rowObject: Record<string, unknown> = {};
        headers.forEach((header, colIndex) => {
          let value: string = (row[colIndex] as string) || '';

          // Trim values if option is enabled (default: true)
          if (options.trimValues !== false && typeof value === 'string') {
            value = value.trim();
          }

          rowObject[header] = value;
        });

        data.push(rowObject);
      });

      return {
        data,
        totalRows: data.length,
        columns: headers,
      };
    } catch (error) {
      if (error instanceof FileCorruptedException) {
        throw error;
      }
      throw new FileCorruptedException(
        `XLSX parsing error: ${(error as Error).message || (error as string)}`,
      );
    }
  }

  /**
   * Determines file type from filename
   */
  private getFileType(filename: string): SupportedFileType {
    const parts = filename.split('.');

    // Se não há ponto no nome do arquivo ou só há um ponto no início (arquivo oculto)
    if (parts.length < 2 || (parts.length === 2 && parts[0] === '')) {
      throw new UnsupportedFileTypeException('unknown');
    }

    const extension = parts.pop()?.toLowerCase();

    if (!extension) {
      throw new UnsupportedFileTypeException('unknown');
    }

    if (extension === 'csv') {
      return SupportedFileType.CSV;
    }

    if (extension === 'xlsx' || extension === 'xls') {
      return SupportedFileType.XLSX;
    }

    throw new UnsupportedFileTypeException(extension);
  }

  /**
   * Validates if all required columns are present
   */
  private validateRequiredColumns(
    fileColumns: string[],
    requiredColumns: string[],
  ): FileValidationResult {
    const missingColumns = requiredColumns.filter(
      (required) =>
        !fileColumns.some(
          (col) => col.toLowerCase().trim() === required.toLowerCase().trim(),
        ),
    );

    return {
      isValid: missingColumns.length === 0,
      missingColumns,
      errors:
        missingColumns.length > 0
          ? [`Missing required columns: ${missingColumns.join(', ')}`]
          : [],
    };
  }

  /**
   * Gets file information without parsing the entire content
   */
  getFileInfo(file: Express.Multer.File): {
    filename: string;
    size: number;
    type: string;
    columns?: string[];
  } {
    const fileType = this.getFileType(file.originalname);

    let columns: string[] = [];

    try {
      if (fileType === SupportedFileType.CSV) {
        // Read only first few lines to get headers
        const firstLines = file.buffer.toString('utf8').split('\n').slice(0, 2);
        if (firstLines.length > 0) {
          columns = firstLines[0]
            .split(',')
            .map((col) => col.trim().replace(/"/g, ''));
        }
      } else if (fileType === SupportedFileType.XLSX) {
        const workbook = XLSX.read(file.buffer, { type: 'buffer' });
        if (workbook.SheetNames.length > 0) {
          const worksheet = workbook.Sheets[workbook.SheetNames[0]];
          const jsonData = XLSX.utils.sheet_to_json(worksheet, {
            header: 1,
          }) as unknown[][];
          if (jsonData.length > 0) {
            columns = jsonData[0].map((header: unknown) =>
              String(header).trim(),
            );
          }
        }
      }
    } catch (error) {
      this.logger.warn(
        `Could not extract columns from ${file.originalname}:`,
        (error as Error).message || (error as string),
      );
    }

    return {
      filename: file.originalname,
      size: file.size,
      type: fileType,
      columns,
    };
  }

  /**
   * Processes file in batches for large files
   * @param file - The uploaded file
   * @param options - Parsing options
   * @param batchSize - Number of rows to process at a time
   * @param onBatch - Callback function called for each batch
   */
  async processFileInBatches(
    file: Express.Multer.File,
    options: FileReaderOptions = {},
    batchSize: number = 1000,
    onBatch: (
      batch: Record<string, unknown>[],
      batchNumber: number,
    ) => Promise<void>,
  ): Promise<{ totalProcessed: number; batches: number }> {
    try {
      this.logger.log(
        `Processing file in batches: ${file.originalname}, batch size: ${batchSize}`,
      );

      const fileType = this.getFileType(file.originalname);

      if (fileType === SupportedFileType.CSV) {
        return await this.processCsvInBatches(
          file,
          options,
          batchSize,
          onBatch,
        );
      } else if (fileType === SupportedFileType.XLSX) {
        return await this.processXlsxInBatches(
          file,
          options,
          batchSize,
          onBatch,
        );
      } else {
        throw new UnsupportedFileTypeException(fileType);
      }
    } catch (error) {
      this.logger.error(
        `Error processing file in batches ${file.originalname}:`,
        error,
      );
      throw new FileProcessingException(
        (error as Error).message || (error as string),
      );
    }
  }

  private async processCsvInBatches(
    file: Express.Multer.File,
    options: FileReaderOptions,
    batchSize: number,
    onBatch: (
      batch: Record<string, unknown>[],
      batchNumber: number,
    ) => Promise<void>,
  ): Promise<{ totalProcessed: number; batches: number }> {
    return new Promise((resolve, reject) => {
      let currentBatch: Record<string, unknown>[] = [];
      let totalProcessed = 0;
      let batchNumber = 0;
      let columns: string[] = [];

      const stream = Readable.from(file.buffer);

      const csvOptions = {
        separator: options.delimiter || ',',
        skipEmptyLines: options.skipEmptyLines !== false,
        headers: true,
      };

      stream
        .pipe(csv(csvOptions))
        .on('headers', (headers: string[]) => {
          columns = headers.map((header) =>
            options.trimValues !== false ? header.trim() : header,
          );

          // Validate required columns if specified
          if (options.requiredColumns && options.requiredColumns.length > 0) {
            const validation = this.validateRequiredColumns(
              columns,
              options.requiredColumns,
            );
            if (!validation.isValid) {
              reject(
                new MissingRequiredColumnsException(validation.missingColumns),
              );
              return;
            }
          }
        })
        .on('data', (row: Record<string, unknown>) => {
          void (async () => {
            try {
              if (options.trimValues !== false) {
                Object.keys(row).forEach((key) => {
                  if (typeof row[key] === 'string') {
                    row[key] = row[key].trim();
                  }
                });
              }

              currentBatch.push(row);
              totalProcessed++;

              if (currentBatch.length >= batchSize) {
                batchNumber++;
                await onBatch([...currentBatch], batchNumber);
                currentBatch = [];
              }
            } catch (error) {
              reject(
                new FileProcessingException(
                  `Row ${totalProcessed}: ${error instanceof Error ? error.message : String(error)}`,
                ),
              );
            }
          })();
        })
        .on('end', () => {
          void (async () => {
            try {
              // Process remaining items in the last batch
              if (currentBatch.length > 0) {
                batchNumber++;
                await onBatch([...currentBatch], batchNumber);
              }

              resolve({ totalProcessed, batches: batchNumber });
            } catch (error) {
              reject(error instanceof Error ? error : new Error(String(error)));
            }
          })();
        })
        .on('error', (error) => {
          reject(
            new FileCorruptedException(
              `CSV parsing error: ${error instanceof Error ? error.message : String(error)}`,
            ),
          );
        });
    });
  }

  private async processXlsxInBatches(
    file: Express.Multer.File,
    options: FileReaderOptions,
    batchSize: number,
    onBatch: (
      batch: Record<string, unknown>[],
      batchNumber: number,
    ) => Promise<void>,
  ): Promise<{ totalProcessed: number; batches: number }> {
    try {
      const workbook = XLSX.read(file.buffer, { type: 'buffer' });

      if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
        throw new FileCorruptedException('No sheets found in Excel file');
      }

      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];

      if (!worksheet) {
        throw new FileCorruptedException(`Sheet "${sheetName}" not found`);
      }

      const jsonData = XLSX.utils.sheet_to_json(worksheet, {
        header: 1,
        defval: '',
        blankrows: !options.skipEmptyLines,
      }) as unknown[][];

      if (jsonData.length === 0) {
        return { totalProcessed: 0, batches: 0 };
      }

      const headers = jsonData[0].map((header: unknown) =>
        options.trimValues !== false ? String(header).trim() : String(header),
      );

      // Validate required columns if specified
      if (options.requiredColumns && options.requiredColumns.length > 0) {
        const validation = this.validateRequiredColumns(
          headers,
          options.requiredColumns,
        );
        if (!validation.isValid) {
          throw new MissingRequiredColumnsException(validation.missingColumns);
        }
      }

      const dataRows = jsonData.slice(1);
      let totalProcessed = 0;
      let batchNumber = 0;

      // Process in batches
      for (let i = 0; i < dataRows.length; i += batchSize) {
        const batchRows = dataRows.slice(i, i + batchSize);
        const batch: Record<string, unknown>[] = [];

        batchRows.forEach((row) => {
          // Skip empty rows if option is enabled
          if (
            options.skipEmptyLines !== false &&
            row.every((cell: unknown) => !cell)
          ) {
            return;
          }

          const rowObject: Record<string, unknown> = {};
          headers.forEach((header, colIndex) => {
            let value = row[colIndex] || '';

            // Trim values if option is enabled (default: true)
            if (options.trimValues !== false && typeof value === 'string') {
              value = value.trim();
            }

            rowObject[header] = value;
          });

          batch.push(rowObject);
          totalProcessed++;
        });

        if (batch.length > 0) {
          batchNumber++;
          await onBatch(batch, batchNumber);
        }
      }

      return { totalProcessed, batches: batchNumber };
    } catch (error) {
      if (
        error instanceof FileCorruptedException ||
        error instanceof MissingRequiredColumnsException
      ) {
        throw error;
      }
      throw new FileCorruptedException(
        `XLSX parsing error: ${(error as Error).message || (error as string)}`,
      );
    }
  }
}
