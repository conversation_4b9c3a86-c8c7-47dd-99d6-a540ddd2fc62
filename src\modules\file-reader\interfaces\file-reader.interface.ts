export interface FileReaderOptions {
  requiredColumns?: string[];
  delimiter?: string;
  encoding?: BufferEncoding;
  skipEmptyLines?: boolean;
  trimValues?: boolean;
}

export interface ParsedFileResult {
  data: Record<string, unknown>[];
  totalRows: number;
  columns: string[];
  errors?: string[];
}

export interface FileValidationResult {
  isValid: boolean;
  missingColumns: string[];
  errors: string[];
}

export enum SupportedFileType {
  CSV = 'csv',
  XLSX = 'xlsx',
}
